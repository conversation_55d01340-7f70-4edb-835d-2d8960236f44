// Development-only mock API service
// This file should NEVER be imported in production builds

import type { FileImport } from './apiData';

// Guard against production usage
if (import.meta.env.PROD) {
  throw new Error('Mock API service should not be used in production');
}

// Mock data for development and testing
const mockFileImports: FileImport[] = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    filename: 'alumni_data_2024.csv',
    file_type: 'csv',
    upload_date: new Date('2024-01-15T10:30:00Z'),
    status: 'completed',
    records_count: 150,
    processed_records: 150,
    errors_count: 0,
    uploaded_by: '<EMAIL>',
    file_size: 2048576,
    processedAt: new Date('2024-01-15T10:35:00Z'),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    filename: 'member_updates.json',
    file_type: 'json',
    upload_date: new Date('2024-01-14T14:20:00Z'),
    status: 'processing',
    records_count: 75,
    processed_records: 45,
    errors_count: 2,
    uploaded_by: '<EMAIL>',
    file_size: 1024000,
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    filename: 'event_data.xlsx',
    file_type: 'xlsx',
    upload_date: new Date('2024-01-13T09:15:00Z'),
    status: 'failed',
    records_count: 0,
    processed_records: 0,
    errors_count: 1,
    uploaded_by: '<EMAIL>',
    file_size: 512000,
    error: 'Invalid file format',
  },
];

export const MockAPIDataService = {
  getData: async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    return { data: mockFileImports };
  },
  
  getFileImports: async (): Promise<FileImport[]> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 200));
    return [...mockFileImports];
  },
  
  updateFileImport: async (id: string, updates: Partial<FileImport>): Promise<void> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 150));
    
    const index = mockFileImports.findIndex(item => item.id === id);
    if (index !== -1) {
      mockFileImports[index] = { ...mockFileImports[index], ...updates };
    }
    
    // Log for development debugging
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log('Mock: Updated file import', id, updates);
    }
  },
  
  exportData: async (format: string): Promise<Blob> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    if (format === 'csv') {
      const csvData = mockFileImports.map(item => 
        `${item.id},${item.filename},${item.file_type},${item.status},${item.records_count}`
      ).join('\n');
      return new Blob([csvData], { type: 'text/csv' });
    } else {
      return new Blob([JSON.stringify(mockFileImports, null, 2)], { type: 'application/json' });
    }
  },
  
  getStatistics: async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      totalUsers: 1250,
      totalPosts: 89,
      totalImports: mockFileImports.length,
      completedImports: mockFileImports.filter(item => item.status === 'completed').length,
      failedImports: mockFileImports.filter(item => item.status === 'failed').length,
      totalRecords: mockFileImports.reduce((sum, item) => sum + item.records_count, 0),
    };
  }
};

// Export mock data for testing
export { mockFileImports };

// Helper function to check if we should use mock data
export const shouldUseMockData = (): boolean => {
  return import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL;
};

// Helper function to get the appropriate service
export const getAPIDataService = () => {
  if (shouldUseMockData()) {
    return MockAPIDataService;
  }
  
  // Import the real service dynamically to avoid bundling in development
  return import('./apiData').then(module => module.APIDataService);
};
